#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI爬虫线程模块
从 crawler_gui_2.py 中拆分出来的线程处理功能
"""

import asyncio
from PyQt5.QtCore import QThread, pyqtSignal
from core.PaginationHandler import PaginationHandler
from core import crawler


class CrawlerThread(QThread):
    """爬虫线程，支持传统分页和动态分页"""
    
    # 定义信号
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)  # 当前进度, 总数
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self._stop_requested = False  # 停止标志
        
    def run(self):
        """线程主执行方法"""
        try:
            # 将日志信号传递给爬虫
            self.config['log_callback'] = self.log_signal.emit

            # 添加停止检查回调
            self.config['stop_check_callback'] = self.is_stop_requested

            # 检查模组配置
            module_config = self.config.get('module_config', {})
            # 强制启用模组配置，除非明确禁用
            use_module_config = self.config.get('use_module_config', True)  # 默认启用

            if use_module_config or module_config.get('enabled', False):
                self.log_signal.emit("✅ 模组配置系统已启用")
                self.config['use_module_config'] = True
            else:
                self.log_signal.emit("ℹ️ 使用传统配置模式")
                self.config['use_module_config'] = False

            # 检查是否使用动态翻页
            pagination_config = self.config.get('pagination_config', {})

            # 检查翻页配置
            enabled = pagination_config.get('enabled', False)
            pagination_type = pagination_config.get('pagination_type')

            # 检查是否已请求停止
            if self._stop_requested:
                self.log_signal.emit("爬取已被用户停止")
                self.finished_signal.emit({"total": 0, "success": 0, "failed": 0, "stopped": True})
                return

            # PaginationHandler已经完全集成到core.crawler.py中，现在可以正常使用动态翻页功能
            if (enabled and pagination_type != '禁用动态翻页'):
                if pagination_type == '手动翻页':
                    self.log_signal.emit(f"📋 启动手动翻页模块")
                    self.log_signal.emit(f"🔧 翻页配置: {pagination_config}")
                    result = self.run_manual_pagination()
                else:
                    self.log_signal.emit(f"🚀 启动动态翻页模块")
                    self.log_signal.emit(f"📋 翻页类型: {pagination_type}")
                    self.log_signal.emit(f"🔧 翻页配置: {pagination_config}")
                    result = self.run_dynamic_pagination()
            else:
                # 使用传统翻页
                self.log_signal.emit("🔄 使用传统翻页模式，调用新版异步爬虫...")
                result = self.run_traditional_crawling()

            # 检查是否在执行过程中被停止
            if self._stop_requested:
                result["stopped"] = True
                self.log_signal.emit("爬取已被用户停止")

            self.finished_signal.emit(result)
        except Exception as e:
            self.log_signal.emit(f"爬取过程中发生错误: {e}")
            self.finished_signal.emit({"total": 0, "success": 0, "failed": 1, "error": str(e)})

    def request_stop(self):
        """请求停止爬取"""
        self._stop_requested = True
        self.log_signal.emit("收到停止请求，正在停止爬取...")

    def is_stop_requested(self):
        """检查是否请求停止"""
        return self._stop_requested

    def _prepare_crawler_config(self):
        """准备爬虫配置，过滤掉不支持的参数"""
        # 爬虫函数支持的参数列表（基于crawl_articles_async函数签名）
        # 注意：已删除所有类型参数和单数形式的选择器参数
        supported_params = {
            'all_articles', 'input_url', 'base_url', 'max_pages', 'start_page',
            'list_container_selector', 'article_item_selector',
            'title_selectors', 'date_selectors', 'source_selectors',
            'content_selectors', 'content_type', 'log_callback',
            'page_suffix', 'url_mode', 'browser_type', 'headless',
            'collect_links', 'mode', 'filters', 'export_filename',
            'classid', 'file_format', 'dynamic_pagination_type',
            'max_workers', 'retry', 'interval', 'stop_check_callback',
            'progress_callback', 'use_module_config', 'field_preset',
            'custom_field_list', 'user_custom_fields', 'use_field_config',
            'skip_pagination_if_cached'
        }

        # 过滤配置，只保留支持的参数
        filtered_config = {}
        for key, value in self.config.items():
            if key in supported_params:
                filtered_config[key] = value

        # 处理字段配置
        field_config = self.config.get('field_config', {})
        if field_config.get('use_field_config', False):
            # 应用字段配置
            try:
                # 尝试导入字段配置管理器
                from core.field_config_manager import apply_field_preset, apply_custom_field_list

                field_preset = field_config.get('field_preset', '')
                custom_field_list = field_config.get('custom_field_list', [])

                if field_preset:
                    self.log_signal.emit(f"🔧 应用字段预设: {field_preset}")
                    apply_field_preset(field_preset)
                elif custom_field_list:
                    self.log_signal.emit(f"🔧 应用自定义字段: {len(custom_field_list)} 个字段")
                    apply_custom_field_list(custom_field_list)
                else:
                    self.log_signal.emit("🔧 使用默认字段配置")

            except ImportError:
                self.log_signal.emit("⚠️ 字段配置功能不可用，使用默认字段")
            except Exception as e:
                self.log_signal.emit(f"⚠️ 应用字段配置失败: {e}")

        # 添加日志回调
        filtered_config['log_callback'] = self.log_signal.emit

        return filtered_config

    def run_traditional_crawling(self):
        """运行传统分页爬取（使用新版异步爬虫）"""
        try:
            # 使用新版异步爬虫
            return asyncio.run(self._async_traditional_crawling())
        except Exception as e:
            self.log_signal.emit(f"传统分页处理失败: {e}")
            return {"total": 0, "success": 0, "failed": 0, "error": str(e)}

    async def _async_traditional_crawling(self):
        """异步传统分页处理"""
        try:
            # 检查是否已请求停止
            if self._stop_requested:
                return {"total": 0, "success": 0, "failed": 0, "stopped": True}

            # 准备爬虫配置，过滤掉不支持的参数
            crawler_config = self._prepare_crawler_config()

            # 添加停止检查回调
            crawler_config['stop_check_callback'] = self.is_stop_requested

            # 添加进度回调
            def progress_callback(current, total):
                self.progress_signal.emit(current, total)

            crawler_config['progress_callback'] = progress_callback

            # 调用新版异步爬虫函数
            result = await crawler.crawl_articles_async(**crawler_config)
            return result
        except Exception as e:
            self.log_signal.emit(f"异步传统分页处理出错: {e}")
            return {"total": 0, "success": 0, "failed": 0, "error": str(e)}

    def run_manual_pagination(self):
        """运行手动翻页"""
        try:
            return asyncio.run(self._async_manual_pagination())
        except Exception as e:
            self.log_signal.emit(f"手动翻页失败: {e}")
            return {"total": 0, "success": 0, "failed": 1, "error": str(e)}

    async def _async_manual_pagination(self):
        """异步手动翻页"""
        from playwright.async_api import async_playwright
        from manual_pagination.manual_pagination_handler import ManualPaginationHandler

        pagination_config = self.config.get('pagination_config', {})
        excel_path = pagination_config.get('excel_file_path', 'manual_pagination/url_templates.xlsx')
        wait_between_pages = pagination_config.get('manual_wait_between_pages', 2000)
        save_progress = pagination_config.get('save_progress', True)

        self.log_signal.emit(f"📋 Excel文件: {excel_path}")
        self.log_signal.emit(f"⏱️ 页面间等待: {wait_between_pages}ms")

        # 检查Excel文件是否存在
        import os
        if not os.path.exists(excel_path):
            error_msg = f"Excel文件不存在: {excel_path}"
            self.log_signal.emit(f"❌ {error_msg}")
            return {"total": 0, "success": 0, "failed": 1, "error": error_msg}

        try:
            async with async_playwright() as p:
                # 启动浏览器
                browser_type = self.config.get('browser_type', 'chromium')
                headless = self.config.get('headless', False)

                if browser_type == 'firefox':
                    browser = await p.firefox.launch(headless=headless)
                elif browser_type == 'webkit':
                    browser = await p.webkit.launch(headless=headless)
                else:
                    browser = await p.chromium.launch(headless=headless)

                context = await browser.new_context()
                page = await context.new_page()

                try:
                    # 创建手动翻页处理器
                    handler = ManualPaginationHandler(page)

                    # 配置文章提取参数
                    extract_config = {
                        'list_container_selector': self.config.get('list_container_selector', 'body'),
                        'article_item_selector': self.config.get('article_item_selector', 'a'),
                        'url_mode': self.config.get('url_mode', 'absolute')
                    }

                    # 处理手动翻页
                    processed_pages, total_pages = await handler.process_manual_pagination(
                        excel_path=excel_path,
                        extract_config=extract_config,
                        timeout=30000,
                        wait_between_pages=wait_between_pages,
                        save_progress=save_progress
                    )

                    # 获取所有文章
                    all_articles = handler.get_all_articles()

                    # 保存结果
                    if all_articles:
                        # 使用现有的保存逻辑
                        from core.excel_writer import ExcelWriter

                        export_filename = self.config.get('export_filename', '手动翻页结果')
                        file_format = self.config.get('file_format', 'xlsx')

                        writer = ExcelWriter()

                        if file_format == 'xlsx':
                            filename = await writer.save_to_excel(all_articles, export_filename)
                        else:
                            filename = await writer.save_to_csv(all_articles, export_filename)

                        self.log_signal.emit(f"✅ 结果已保存到: {filename}")

                    # 获取统计信息
                    stats = handler.get_statistics()

                    self.log_signal.emit(f"📊 处理完成:")
                    self.log_signal.emit(f"   - 成功页面: {processed_pages}/{total_pages}")
                    self.log_signal.emit(f"   - 提取文章: {stats['total_articles']} 篇")
                    self.log_signal.emit(f"   - 成功率: {stats['success_rate']:.1f}%")

                    return {
                        "total": total_pages,
                        "success": processed_pages,
                        "failed": total_pages - processed_pages,
                        "articles_count": len(all_articles)
                    }

                finally:
                    await context.close()
                    await browser.close()

        except Exception as e:
            self.log_signal.emit(f"手动翻页处理失败: {e}")
            return {"total": 0, "success": 0, "failed": 1, "error": str(e)}

    def run_dynamic_pagination(self):
        """运行动态翻页爬取"""
        try:
            # 运行异步动态翻页
            return asyncio.run(self._async_dynamic_pagination())
        except Exception as e:
            self.log_signal.emit(f"动态翻页处理失败: {e}")
            # 回退到传统翻页
            self.log_signal.emit("回退到传统翻页模式...")
            return self.run_traditional_crawling()

    async def _async_dynamic_pagination(self):
        """异步动态翻页处理 - 使用集成的PaginationHandler"""
        try:
            # 检查是否已请求停止
            if self._stop_requested:
                return {"total": 0, "success": 0, "failed": 0, "stopped": True}

            from playwright.async_api import async_playwright

            pagination_config = self.config.get('pagination_config', {})
            pagination_type = pagination_config.get('pagination_type', '点击翻页')

            self.log_signal.emit(f"✅ 使用集成的动态翻页功能: {pagination_type}")

            # 获取配置参数
            input_url = self.config.get('input_url')
            max_pages = self.config.get('max_pages', 5)
            list_container_selector = self.config.get('list_container_selector', '.main')
            article_item_selector = self.config.get('article_item_selector', '.clearfix.ty_list li a')

            # 翻页配置
            next_button_selector = pagination_config.get('next_button_selector', 'a.next:not(.lose)')

            async with async_playwright() as p:
                # 启动浏览器 - 对于动态翻页，某些网站需要特殊配置
                headless_mode = True
                pagination_compatible = False

                # 检查是否需要翻页兼容模式（针对已知的问题网站）
                if any(domain in input_url for domain in ['tjszx.gov.cn']):
                    headless_mode = False
                    pagination_compatible = True
                    self.log_signal.emit("🔍 检测到需要翻页兼容模式的网站，启用兼容配置")

                browser, context, page = await crawler.launch_browser(
                    p,
                    headless=headless_mode,
                    pagination_compatible=pagination_compatible
                )

                try:
                    # 使用完整的PaginationHandler
                    # 直接使用 PaginationHandler.py 的 PaginationHandler
                    handler = PaginationHandler(page)

                    # 访问起始页面
                    await page.goto(input_url)
                    self.log_signal.emit(f"访问起始页面: {input_url}")

                    # 准备文章提取配置
                    extract_config = {
                        'list_container_selector': list_container_selector,
                        'article_item_selector': article_item_selector,
                        'title_selector': self.config.get('title_selector'),
                        'save_dir': "动态翻页结果",
                        'page_title': "动态翻页结果",
                        'classid': self.config.get('classid', ''),
                        'base_url': self.config.get('base_url', input_url),  # 如果没有base_url，使用input_url
                        'url_mode': self.config.get('url_mode', 'absolute')  # 修改默认为absolute
                    }

                    # 检查是否在访问页面后被停止
                    if self._stop_requested:
                        await context.close()
                        await browser.close()
                        return {"total": 0, "success": 0, "failed": 0, "stopped": True}

                    # 根据翻页类型执行相应的翻页操作
                    if pagination_type == '点击翻页':
                        # 获取翻页模式配置，默认使用智能模式
                        pagination_mode = pagination_config.get('pagination_mode', 'smart')  # smart/simple/standard
                        use_simple_pagination = pagination_mode == 'simple'
                        auto_detect_pagination = pagination_mode != 'simple'  # 简单模式不使用自动检测

                        if use_simple_pagination:
                            self.log_signal.emit("🚀 使用简单翻页模式（更稳定的翻页逻辑）")
                        elif pagination_mode == 'smart':
                            self.log_signal.emit("🧠 使用智能翻页模式（自动检测 + 回退机制）")
                        else:
                            self.log_signal.emit("⚙️ 使用标准翻页模式")

                        pages_processed = await handler.click_pagination(
                            next_button_selector=next_button_selector,
                            max_pages=max_pages,
                            content_ready_selector=pagination_config.get('content_ready_selector'),
                            timeout=pagination_config.get('timeout', 10000),
                            wait_after_click=pagination_config.get('wait_after_click', 2000),
                            disabled_check=pagination_config.get('disabled_check', True),
                            extract_articles_config=extract_config,
                            stop_on_duplicate_last_item=pagination_config.get('stop_on_duplicate_last_item', True),
                            use_simple_pagination=use_simple_pagination,
                            auto_detect_pagination=auto_detect_pagination
                        )
                        self.log_signal.emit(f"点击翻页完成，处理了 {pages_processed} 页")
                    elif pagination_type == '滚动翻页':
                        # 滚动翻页处理
                        scroll_container_selector = pagination_config.get('scroll_container_selector', 'body')
                        scroll_step = pagination_config.get('scroll_step', 800)
                        scroll_delay = pagination_config.get('scroll_delay', 2000)
                        load_indicator_selector = pagination_config.get('load_indicator_selector', '')

                        self.log_signal.emit(f"开始滚动翻页，容器: {scroll_container_selector}")
                        pages_processed = await handler.scroll_pagination(
                            scroll_container_selector=scroll_container_selector,
                            scroll_step=scroll_step,
                            scroll_delay=scroll_delay,
                            max_scrolls=max_pages,
                            load_indicator_selector=load_indicator_selector if load_indicator_selector else None,
                            extract_articles_config=extract_config
                        )
                        self.log_signal.emit(f"滚动翻页完成，处理了 {pages_processed} 次滚动")
                    else:
                        self.log_signal.emit(f"暂不支持的翻页类型: {pagination_type}，回退到传统翻页")
                        await context.close()
                        await browser.close()
                        return await self._async_traditional_crawling()

                    # 获取所有收集到的文章链接
                    all_articles = handler.get_all_articles()
                    self.log_signal.emit(f"动态翻页完成，共收集到 {len(all_articles)} 篇文章链接")

                    # 关闭浏览器
                    await context.close()
                    await browser.close()

                    # 如果收集到文章，使用异步爬虫处理
                    if all_articles:
                        self.log_signal.emit("开始处理收集的文章链接...")

                        # 使用新版异步爬虫处理收集到的文章
                        result = await crawler.crawl_articles_async(
                            all_articles=all_articles,
                            input_url=self.config.get('input_url', ''),  # 添加input_url参数，用于缓存
                            config_group=self.config.get('config_group', 'default'),  # 添加config_group参数，用于缓存
                            content_selectors=self.config.get('content_selectors', []),
                            title_selectors=self.config.get('title_selectors'),
                            date_selectors=self.config.get('date_selectors'),
                            source_selectors=self.config.get('source_selectors'),
                            collect_links=self.config.get('collect_links', True),
                            mode=self.config.get('mode', 'balance'),
                            filters=self.config.get('filters'),
                            export_filename=self.config.get('export_filename'),
                            classid=self.config.get('classid', ''),
                            file_format=self.config.get('file_format', 'CSV'),
                            retry=self.config.get('retry', 2),
                            interval=self.config.get('interval', 0),
                            max_workers=self.config.get('max_workers', 5),
                            use_module_config=self.config.get('use_module_config', True),  # 启用模组配置
                            # 字段配置参数
                            field_preset=self.config.get('field_preset', ''),
                            custom_field_list=self.config.get('custom_field_list', []),
                            user_custom_fields=self.config.get('user_custom_fields', {}),
                            use_field_config=self.config.get('use_field_config', False),
                            log_callback=self.log_signal.emit
                        )

                        self.log_signal.emit("🎉 动态翻页文章处理完成！")
                        self.log_signal.emit(f"📊 处理结果: 总计{result.get('total', 0)}篇, 成功{result.get('success', 0)}篇, 失败{result.get('failed', 0)}篇")
                        return result
                    else:
                        self.log_signal.emit("⚠️ 动态翻页未收集到任何文章链接")
                        return {"total": 0, "success": 0, "failed": 0}

                except Exception as e:
                    self.log_signal.emit(f"动态翻页处理出错: {e}")
                    await context.close()
                    await browser.close()
                    # 回退到传统翻页
                    self.log_signal.emit("回退到传统翻页模式...")
                    return await self._async_traditional_crawling()

        except Exception as e:
            self.log_signal.emit(f"动态翻页异步处理失败: {e}")
            # 回退到传统翻页
            self.log_signal.emit("回退到传统翻页模式...")
            return await self._async_traditional_crawling()


class CrawlerThreadManager:
    """爬虫线程管理器"""
    
    def __init__(self):
        self.crawler_thread = None
    
    def start_crawling(self, config, log_callback, finished_callback, progress_callback=None):
        """
        启动爬取任务
        
        Args:
            config: 爬取配置
            log_callback: 日志回调函数
            finished_callback: 完成回调函数
            progress_callback: 进度回调函数（可选）
        """
        if self.crawler_thread and self.crawler_thread.isRunning():
            log_callback("爬取任务正在进行中，请等待完成或先停止当前任务")
            return False
        
        self.crawler_thread = CrawlerThread(config)
        self.crawler_thread.log_signal.connect(log_callback)
        self.crawler_thread.finished_signal.connect(finished_callback)
        
        if progress_callback:
            self.crawler_thread.progress_signal.connect(progress_callback)
        
        self.crawler_thread.start()
        return True
    
    def stop_crawling(self, log_callback):
        """停止爬取任务"""
        if self.crawler_thread and self.crawler_thread.isRunning():
            log_callback("用户请求停止爬取...")

            # 首先尝试优雅停止
            self.crawler_thread.request_stop()

            # 等待线程自然结束，最多等待5秒
            if self.crawler_thread.wait(5000):  # 5秒超时
                log_callback("爬取任务已优雅停止")
            else:
                # 如果5秒内没有停止，强制终止
                log_callback("强制终止爬取任务...")
                self.crawler_thread.terminate()
                self.crawler_thread.wait()
                log_callback("爬取任务已强制终止")

            return True
        return False
    
    def is_running(self):
        """检查爬取任务是否正在运行"""
        return self.crawler_thread and self.crawler_thread.isRunning()


if __name__ == "__main__":
    # 测试代码
    print("GUI爬虫线程模块已加载")
    print("主要功能:")
    print("- CrawlerThread: 支持传统分页和动态分页的爬虫线程")
    print("- CrawlerThreadManager: 爬虫线程管理器")
