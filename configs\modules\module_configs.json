{"微信公众号": {"name": "微信公众号", "description": "处理微信公众号文章的模组配置", "domain_patterns": ["mp.weixin.qq.com"], "url_patterns": [], "config": {"title_selectors": ["#activity-name", ".rich_media_title", "h1.rich_media_title", ".rich_media_area_primary h1", "[data-role='title']"], "content_selectors": ["#js_content"], "content_type": "CSS", "date_selectors": ["#publish_time", ".rich_media_meta_text", "#post-date", ".rich_media_meta_list .rich_media_meta_text"], "source_selectors": [".rich_media_meta_nickname", "#js_name", ".profile_nickname", ".rich_media_meta_list .rich_media_meta_nickname"], "mode": "safe", "collect_links": false, "retry": 5, "interval": 3.0, "max_workers": 5, "headless": true, "page_load_strategy": "eager"}}, "海南政协": {"name": "海南政协", "description": "从配置组 '海南政协' 创建的模组", "domain_patterns": ["zx.haikou.gov.cn"], "url_patterns": [], "config": {"title_selectors": [".maincon-t"], "content_selectors": [".maincon-c"], "content_type": "CSS", "date_selectors": [".maincon-i"], "source_selectors": [".maincon-i"], "mode": "safe", "collect_links": false, "retry": 2, "interval": 0.6, "max_workers": 5, "headless": true, "page_load_strategy": "eager"}}, "match_priority": ["海南政协", "微信公众号"]}