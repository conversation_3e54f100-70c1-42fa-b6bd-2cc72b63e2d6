import json
import os
from collections import OrderedDict
from datetime import datetime
from typing import Dict, List, Optional, Any

class ConfigManager:
    """配置管理器 - 支持4级分类结构（单例模式）"""

    _instance = None
    _initialized = False

    def __new__(cls, config_file="configs/app/config.json", max_groups=100):
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, config_file="configs/app/config.json", max_groups=100):
        if not self._initialized:
            self.config_file = config_file
            self.max_groups = max_groups
            self.config = self.load_config()
            ConfigManager._initialized = True
        
    def load_config(self):
        """加载配置文件，如果不存在则创建默认配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f, object_pairs_hook=OrderedDict)
                    print(f"✅ 成功加载配置文件: {self.config_file}")

                    # 检查并迁移旧格式配置
                    if self._needs_migration(config):
                        print("🔄 检测到旧格式配置，正在迁移...")
                        config = self._migrate_config(config)
                        print("✅ 配置迁移完成")

                    return config
            except json.JSONDecodeError as e:
                print(f"❌ JSON格式错误 {self.config_file}: {e}")
                print("🔧 使用默认配置")
                return self.create_default_config()
            except Exception as e:
                print(f"❌ 加载配置文件失败 {self.config_file}: {e}")
                print("🔧 使用默认配置")
                return self.create_default_config()
        else:
            print(f"⚠️ 配置文件不存在: {self.config_file}")
            print("🔧 创建默认配置")
            return self.create_default_config()

    def _needs_migration(self, config: Dict[str, Any]) -> bool:
        """检查是否需要迁移配置"""
        # 如果没有categories字段，说明是旧格式
        return "categories" not in config

    def _migrate_config(self, old_config: Dict[str, Any]) -> Dict[str, Any]:
        """迁移旧格式配置到新格式"""
        try:
            # 创建新的配置结构
            new_config = OrderedDict()
            new_config["last_used"] = old_config.get("last_used", "default")

            # 创建默认三级分类结构
            new_config["categories"] = OrderedDict({
                "政府机构": {
                    "description": "从旧配置迁移的政府机构分类",
                    "subcategories": {
                        "人大系统": {
                            "description": "人大系统配置",
                            "subcategories": {
                                "地方人大": {
                                    "configs": [],
                                    "description": "地方人大配置"
                                }
                            }
                        }
                    },
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
            })

            # 迁移配置组
            new_config["groups"] = OrderedDict()
            old_groups = old_config.get("groups", {})

            for group_name, group_config in old_groups.items():
                # 为每个配置组添加三级分类路径和缓存信息
                new_group_config = group_config.copy()
                new_group_config["category_path"] = "政府机构/人大系统/地方人大"

                # 添加缓存结构
                if "cache" not in new_group_config:
                    new_group_config["cache"] = {
                        "last_update": None,
                        "last_urls": [],
                        "total_articles": 0,
                        "success_rate": 0.0,
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat()
                    }

                new_config["groups"][group_name] = new_group_config

                # 添加到默认三级分类
                new_config["categories"]["政府机构"]["subcategories"]["人大系统"]["subcategories"]["地方人大"]["configs"].append(group_name)

            # 保存迁移后的配置
            self.config = new_config
            self.save_config()

            return new_config

        except Exception as e:
            print(f"❌ 配置迁移失败: {e}")
            return old_config
    
    def create_default_config(self):
        """创建默认配置，包含所有参数字段和新的三级分类、缓存结构"""
        return OrderedDict({
            "last_used": "default",
            "categories": OrderedDict({
                "政府机构": {
                    "description": "政府机构相关配置",
                    "subcategories": {
                        "人大系统": {
                            "description": "人大系统配置",
                            "subcategories": {
                                "全国人大": {
                                    "configs": [],
                                    "description": "全国人大配置"
                                },
                                "地方人大": {
                                    "configs": ["default"],
                                    "description": "地方人大配置"
                                }
                            }
                        },
                        "政协系统": {
                            "description": "政协系统配置",
                            "subcategories": {
                                "全国政协": {
                                    "configs": [],
                                    "description": "全国政协配置"
                                },
                                "地方政协": {
                                    "configs": [],
                                    "description": "地方政协配置"
                                }
                            }
                        }
                    },
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
            }),
            "groups": OrderedDict({
                "default": {
                    "category_path": "政府机构/人大系统/地方人大",
                    "input_url": "",
                    "base_url": "",
                    "max_pages": "0",
                    "list_container_selector": "",
                    "article_item_selector": "",
                    "title_selectors": [],  # 使用复数形式，支持多选择器
                    "content_selectors": [],
                    "content_type": "CSS",
                    "date_selectors": [],  # 使用复数形式，支持多选择器
                    "source_selectors": [],  # 使用复数形式，支持多选择器
                    "page_suffix": "index_{n}.html",
                    "page_suffix_start": 1,
                    "url_mode": "absolute",
                    "browser": "Firefox",
                    "headless": True,
                    "window_size": "",
                    "page_load_strategy": "normal",
                    "collect_links": True,
                    "mode": "balance",
                    "filters": [],
                    "export_filename": "",
                    "classid": "",
                    "file_format": "CSV",
                    "cache": {
                        "last_update": None,
                        "last_urls": [],
                        "total_articles": 0,
                        "success_rate": 0.0,
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat()
                    }
                }
            })
        })
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get_groups(self):
        """获取所有配置组名称"""
        return list(self.config["groups"].keys())
    
    def get_group(self, group_name):
        """获取指定配置组"""
        return self.config["groups"].get(group_name)
    
    def get_current_group(self):
        """获取当前使用的配置组"""
        return self.config["last_used"]
    
    def add_group(self, group_name, config_data, category_path="政府机构/人大系统/地方人大/监督纵横"):
        """添加新的配置组（使用完整4级路径作为配置组名称）"""
        try:
            groups = self.config["groups"]

            # 确保category_path是完整的4级路径
            parts = category_path.split("/")
            if len(parts) != 4:
                # 如果不是4级路径，补充为4级
                if len(parts) == 3:
                    # 根据配置组名称推断第4级
                    fourth_level = self._infer_fourth_level_from_name(group_name, parts[2])
                    category_path = f"{category_path}/{fourth_level}"
                else:
                    category_path = "政府机构/人大系统/地方人大/监督纵横"
                parts = category_path.split("/")

            # 使用完整4级路径作为配置组名称
            full_path_name = category_path

            # 确保4级分类存在
            parent, sub, third, fourth = parts
            self.add_four_level_category(parent, sub, third, fourth)

            # 检查是否存在旧的配置组（可能使用旧名称）
            old_config = None
            old_key_to_remove = None

            # 查找可能的旧配置组
            if group_name in groups:
                old_config = groups[group_name]
                old_key_to_remove = group_name
            elif full_path_name in groups:
                old_config = groups[full_path_name]
                old_key_to_remove = full_path_name

            if old_config:
                # 合并旧配置，防止字段丢失
                merged_config = old_config.copy()
                # 嵌套字典（如module_config、pagination_config）也要合并
                for k, v in config_data.items():
                    if isinstance(v, dict) and k in merged_config and isinstance(merged_config[k], dict):
                        merged = merged_config[k].copy()
                        merged.update(v)
                        merged_config[k] = merged
                    else:
                        merged_config[k] = v

                # 如果旧键名与新键名不同，需要删除旧键
                if old_key_to_remove != full_path_name:
                    del groups[old_key_to_remove]
                    # 从旧分类中移除引用
                    old_category_path = old_config.get("category_path")
                    if old_category_path:
                        self._remove_config_from_old_path(old_key_to_remove, old_category_path)

                # 更新分类路径
                merged_config["category_path"] = category_path
                groups[full_path_name] = merged_config
            else:
                # 新配置组
                # 如果达到最大组数限制，删除最旧的一个
                if len(groups) >= self.max_groups:
                    oldest = next(iter(groups))
                    # 从分类中也移除
                    oldest_config = groups[oldest]
                    oldest_category_path = oldest_config.get("category_path")
                    if oldest_category_path:
                        self._remove_config_from_old_path(oldest, oldest_category_path)
                    del groups[oldest]

                # 添加分类路径和缓存结构
                config_data["category_path"] = category_path
                if "cache" not in config_data:
                    config_data["cache"] = {
                        "last_update": None,
                        "last_urls": [],
                        "total_articles": 0,
                        "success_rate": 0.0,
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat()
                    }

                groups[full_path_name] = config_data

            # 添加到4级分类中
            parent, sub, third, fourth = parts
            fourth_cat = (self.config.get("categories", {})
                        .get(parent, {})
                        .get("subcategories", {})
                        .get(sub, {})
                        .get("subcategories", {})
                        .get(third, {})
                        .get("subcategories", {})
                        .get(fourth, {}))
            if "configs" in fourth_cat and full_path_name not in fourth_cat["configs"]:
                fourth_cat["configs"].append(full_path_name)

            self.config["last_used"] = full_path_name
            self.save_config()
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False

    def _infer_fourth_level_from_name(self, config_name, third_level):
        """根据配置组名称和第3级分类推断第4级分类"""
        try:
            # 如果配置组名称包含下划线，取后半部分作为第4级分类
            if "_" in config_name:
                return config_name.split("_")[-1]

            # 根据第3级分类推断常见的第4级分类
            if "政协" in third_level:
                if "提案" in config_name:
                    return "提案工作"
                elif "委员" in config_name:
                    return "委员工作"
                else:
                    return "提案工作"
            elif "人大" in third_level:
                if "监督" in config_name or "纵横" in config_name:
                    return "监督纵横"
                elif "代表" in config_name:
                    return "代表工作"
                elif "制度" in config_name:
                    return "人大制度"
                elif "常委会" in config_name:
                    return "常委会工作"
                else:
                    return "监督纵横"
            else:
                return "常规工作"
        except:
            return "常规工作"
    
    def delete_group(self, group_name):
        """删除配置组"""
        if group_name in self.config["groups"]:
            # 从分类中移除配置组引用
            self._remove_config_from_old_path(group_name, None)

            del self.config["groups"][group_name]

            # 如果删除的是当前使用的配置组，重置为默认
            if self.config["last_used"] == group_name:
                remaining_groups = list(self.config["groups"].keys())
                self.config["last_used"] = remaining_groups[0] if remaining_groups else None

            self.save_config()
            return True
        return False

    def _remove_config_from_old_path(self, config_name, old_category_path=None):
        """从旧的分类路径中移除配置组引用"""
        try:
            # 如果没有提供旧路径，从配置组数据中获取
            if not old_category_path:
                config_data = self.config["groups"].get(config_name)
                if config_data:
                    old_category_path = config_data.get("category_path")

            if not old_category_path:
                return

            parts = old_category_path.split("/")
            if len(parts) == 4:
                parent, sub, third, fourth = parts
                fourth_cat = (self.config.get("categories", {})
                            .get(parent, {})
                            .get("subcategories", {})
                            .get(sub, {})
                            .get("subcategories", {})
                            .get(third, {})
                            .get("subcategories", {})
                            .get(fourth, {}))
                if "configs" in fourth_cat and config_name in fourth_cat["configs"]:
                    fourth_cat["configs"].remove(config_name)
            elif len(parts) == 3:
                parent, sub, child = parts
                child_cat = (self.config.get("categories", {})
                           .get(parent, {})
                           .get("subcategories", {})
                           .get(sub, {})
                           .get("subcategories", {})
                           .get(child, {}))
                if "configs" in child_cat and config_name in child_cat["configs"]:
                    child_cat["configs"].remove(config_name)
        except Exception as e:
            print(f"移除配置组引用失败: {e}")
    
    def set_current_group(self, group_name):
        """设置当前使用的配置组"""
        if group_name in self.config["groups"]:
            self.config["last_used"] = group_name
            self.save_config()
            return True
        return False

    # ==================== 新增：分类管理功能 ====================

    def get_categories(self) -> Dict[str, Any]:
        """获取所有分类"""
        return self.config.get("categories", {})

    def add_category(self, category_name: str, description: str = "") -> bool:
        """添加新分类"""
        try:
            if "categories" not in self.config:
                self.config["categories"] = OrderedDict()

            if category_name not in self.config["categories"]:
                self.config["categories"][category_name] = {
                    "configs": [],
                    "description": description,
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
                self.save_config()
                return True
            return False
        except Exception as e:
            print(f"添加分类失败: {e}")
            return False

    def delete_category(self, category_name: str) -> bool:
        """删除分类（将其下的配置组移动到默认分类）"""
        try:
            if category_name not in self.config.get("categories", {}):
                return False

            # 确保默认分类存在
            if "默认分类" not in self.config["categories"]:
                self.add_category("默认分类", "默认配置分类")

            # 移动配置组到默认分类
            configs_to_move = self.config["categories"][category_name].get("configs", [])
            for config_name in configs_to_move:
                if config_name in self.config["groups"]:
                    self.config["groups"][config_name]["category"] = "默认分类"
                    if config_name not in self.config["categories"]["默认分类"]["configs"]:
                        self.config["categories"]["默认分类"]["configs"].append(config_name)

            # 删除分类
            del self.config["categories"][category_name]
            self.save_config()
            return True
        except Exception as e:
            print(f"删除分类失败: {e}")
            return False

    def move_config_to_category(self, config_name: str, target_category: str) -> bool:
        """将配置组移动到指定分类"""
        try:
            if config_name not in self.config["groups"]:
                return False

            if target_category not in self.config["categories"]:
                return False

            # 从原分类中移除
            old_category = self.config["groups"][config_name].get("category")
            if old_category and old_category in self.config["categories"]:
                if config_name in self.config["categories"][old_category]["configs"]:
                    self.config["categories"][old_category]["configs"].remove(config_name)

            # 添加到新分类
            self.config["groups"][config_name]["category"] = target_category
            if config_name not in self.config["categories"][target_category]["configs"]:
                self.config["categories"][target_category]["configs"].append(config_name)

            self.save_config()
            return True
        except Exception as e:
            print(f"移动配置组失败: {e}")
            return False

    def get_configs_by_category(self, category_name: str) -> List[str]:
        """获取指定分类下的所有配置组（兼容旧版本）"""
        category = self.config.get("categories", {}).get(category_name, {})
        return category.get("configs", [])

    # ==================== 新增：三级分类管理功能 ====================

    def get_parent_categories(self) -> List[str]:
        """获取所有父级分类"""
        return list(self.config.get("categories", {}).keys())

    def get_sub_categories(self, parent_category: str) -> List[str]:
        """获取指定父级分类下的次级分类"""
        parent = self.config.get("categories", {}).get(parent_category, {})
        return list(parent.get("subcategories", {}).keys())

    def get_child_categories(self, parent_category: str, sub_category: str) -> List[str]:
        """获取指定次级分类下的子级分类（第3级）"""
        parent = self.config.get("categories", {}).get(parent_category, {})
        sub = parent.get("subcategories", {}).get(sub_category, {})
        return list(sub.get("subcategories", {}).keys())

    def get_fourth_categories(self, parent_category: str, sub_category: str, third_category: str) -> List[str]:
        """获取指定第3级分类下的第4级分类（配置组容器）"""
        parent = self.config.get("categories", {}).get(parent_category, {})
        sub = parent.get("subcategories", {}).get(sub_category, {})
        third = sub.get("subcategories", {}).get(third_category, {})
        return list(third.get("subcategories", {}).keys())

    def get_configs_by_category_path(self, category_path: str) -> List[str]:
        """根据分类路径获取配置组列表（支持4级结构）"""
        try:
            parts = category_path.split("/")
            if len(parts) == 3:
                # 兼容旧的3级结构
                parent, sub, child = parts
                parent_cat = self.config.get("categories", {}).get(parent, {})
                sub_cat = parent_cat.get("subcategories", {}).get(sub, {})
                child_cat = sub_cat.get("subcategories", {}).get(child, {})
                return child_cat.get("configs", [])
            elif len(parts) == 4:
                # 新的4级结构
                parent, sub, third, fourth = parts
                parent_cat = self.config.get("categories", {}).get(parent, {})
                sub_cat = parent_cat.get("subcategories", {}).get(sub, {})
                third_cat = sub_cat.get("subcategories", {}).get(third, {})
                fourth_cat = third_cat.get("subcategories", {}).get(fourth, {})
                return fourth_cat.get("configs", [])
            else:
                return []
        except:
            return []

    def add_three_level_category(self, parent: str, sub: str, child: str, description: str = "") -> bool:
        """添加三级分类"""
        try:
            categories = self.config.setdefault("categories", OrderedDict())

            # 确保父级分类存在
            if parent not in categories:
                categories[parent] = {
                    "description": f"{parent}分类",
                    "subcategories": OrderedDict(),
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }

            # 确保次级分类存在
            parent_cat = categories[parent]
            subcategories = parent_cat.setdefault("subcategories", OrderedDict())
            if sub not in subcategories:
                subcategories[sub] = {
                    "description": f"{sub}分类",
                    "subcategories": OrderedDict()
                }

            # 添加子级分类
            sub_cat = subcategories[sub]
            child_subcategories = sub_cat.setdefault("subcategories", OrderedDict())
            if child not in child_subcategories:
                child_subcategories[child] = {
                    "configs": [],
                    "description": description or f"{child}配置"
                }

                # 更新时间戳
                parent_cat["updated_at"] = datetime.now().isoformat()
                self.save_config()
                return True

            return False
        except Exception as e:
            print(f"添加三级分类失败: {e}")
            return False

    def move_config_to_category_path(self, config_name: str, category_path: str) -> bool:
        """将配置组移动到指定的分类路径（支持3级和4级结构）"""
        try:
            if config_name not in self.config["groups"]:
                return False

            parts = category_path.split("/")
            if len(parts) not in [3, 4]:
                return False

            # 从旧分类中移除
            old_category_path = self.config["groups"][config_name].get("category_path")
            if old_category_path:
                self._remove_config_from_old_path(config_name, old_category_path)

            # 添加到新分类
            if len(parts) == 3:
                # 3级结构
                parent, sub, child = parts
                self.add_three_level_category(parent, sub, child)
                target_cat = (self.config.get("categories", {})
                            .get(parent, {})
                            .get("subcategories", {})
                            .get(sub, {})
                            .get("subcategories", {})
                            .get(child, {}))
            else:
                # 4级结构
                parent, sub, third, fourth = parts
                self.add_four_level_category(parent, sub, third, fourth)
                target_cat = (self.config.get("categories", {})
                            .get(parent, {})
                            .get("subcategories", {})
                            .get(sub, {})
                            .get("subcategories", {})
                            .get(third, {})
                            .get("subcategories", {})
                            .get(fourth, {}))

            if "configs" in target_cat:
                if config_name not in target_cat["configs"]:
                    target_cat["configs"].append(config_name)

                # 更新配置组的分类路径
                self.config["groups"][config_name]["category_path"] = category_path

                self.save_config()
                return True

            return False
        except Exception as e:
            print(f"移动配置组失败: {e}")
            return False

    def delete_parent_category(self, parent_name: str) -> bool:
        """删除父级分类"""
        try:
            categories = self.config.get("categories", {})
            if parent_name not in categories:
                return False

            # 移动所有配置组到默认分类
            self._move_all_configs_from_parent(parent_name)

            # 删除父级分类
            del categories[parent_name]
            self.save_config()
            return True
        except Exception as e:
            print(f"删除父级分类失败: {e}")
            return False

    def delete_sub_category(self, parent_name: str, sub_name: str) -> bool:
        """删除次级分类"""
        try:
            parent_cat = self.config.get("categories", {}).get(parent_name, {})
            subcategories = parent_cat.get("subcategories", {})

            if sub_name not in subcategories:
                return False

            # 移动所有配置组到默认分类
            self._move_all_configs_from_sub(parent_name, sub_name)

            # 删除次级分类
            del subcategories[sub_name]
            self.save_config()
            return True
        except Exception as e:
            print(f"删除次级分类失败: {e}")
            return False

    def delete_child_category(self, parent_name: str, sub_name: str, child_name: str) -> bool:
        """删除子级分类"""
        try:
            child_cat = (self.config.get("categories", {})
                        .get(parent_name, {})
                        .get("subcategories", {})
                        .get(sub_name, {})
                        .get("subcategories", {})
                        .get(child_name, {}))

            if not child_cat:
                return False

            # 移动所有配置组到默认分类
            configs = child_cat.get("configs", [])
            default_path = "政府机构/人大系统/地方人大"
            for config_name in configs:
                self.move_config_to_category_path(config_name, default_path)

            # 删除子级分类
            parent_cat = self.config["categories"][parent_name]
            sub_cat = parent_cat["subcategories"][sub_name]
            del sub_cat["subcategories"][child_name]

            self.save_config()
            return True
        except Exception as e:
            print(f"删除子级分类失败: {e}")
            return False

    def _move_all_configs_from_parent(self, parent_name: str):
        """将父级分类下的所有配置组移动到默认4级分类"""
        try:
            parent_cat = self.config.get("categories", {}).get(parent_name, {})
            subcategories = parent_cat.get("subcategories", {})
            default_path = "政府机构/人大系统/地方人大/监督纵横"

            # 确保默认4级分类存在
            self.add_four_level_category("政府机构", "人大系统", "地方人大", "监督纵横")

            for sub_name, sub_cat in subcategories.items():
                child_subcategories = sub_cat.get("subcategories", {})
                for child_name, child_cat in child_subcategories.items():
                    # 处理3级分类下的配置组
                    configs = child_cat.get("configs", [])
                    for config_name in configs:
                        self.move_config_to_category_path(config_name, default_path)

                    # 处理4级分类下的配置组
                    fourth_subcategories = child_cat.get("subcategories", {})
                    for fourth_name, fourth_cat in fourth_subcategories.items():
                        fourth_configs = fourth_cat.get("configs", [])
                        for config_name in fourth_configs:
                            self.move_config_to_category_path(config_name, default_path)
        except Exception as e:
            print(f"移动父级分类下的配置组失败: {e}")

    def _move_all_configs_from_sub(self, parent_name: str, sub_name: str):
        """将次级分类下的所有配置组移动到默认分类"""
        try:
            sub_cat = (self.config.get("categories", {})
                      .get(parent_name, {})
                      .get("subcategories", {})
                      .get(sub_name, {}))

            child_subcategories = sub_cat.get("subcategories", {})
            default_path = "政府机构/人大系统/地方人大"

            for child_name, child_cat in child_subcategories.items():
                configs = child_cat.get("configs", [])
                for config_name in configs:
                    self.move_config_to_category_path(config_name, default_path)
        except Exception as e:
            print(f"移动次级分类下的配置组失败: {e}")

    def rename_parent_category(self, old_name: str, new_name: str, new_desc: str = "") -> bool:
        """重命名父级分类"""
        try:
            categories = self.config.get("categories", {})
            if old_name not in categories or new_name in categories:
                return False

            # 复制分类数据
            category_data = categories[old_name].copy()
            if new_desc:
                category_data["description"] = new_desc
            category_data["updated_at"] = datetime.now().isoformat()

            # 更新所有配置组的分类路径
            self._update_config_paths_for_parent_rename(old_name, new_name)

            # 删除旧分类，添加新分类
            del categories[old_name]
            categories[new_name] = category_data

            self.save_config()
            return True
        except Exception as e:
            print(f"重命名父级分类失败: {e}")
            return False

    def rename_sub_category(self, parent_name: str, old_name: str, new_name: str, new_desc: str = "") -> bool:
        """重命名次级分类"""
        try:
            parent_cat = self.config.get("categories", {}).get(parent_name, {})
            subcategories = parent_cat.get("subcategories", {})

            if old_name not in subcategories or new_name in subcategories:
                return False

            # 复制分类数据
            sub_data = subcategories[old_name].copy()
            if new_desc:
                sub_data["description"] = new_desc

            # 更新所有配置组的分类路径
            self._update_config_paths_for_sub_rename(parent_name, old_name, new_name)

            # 删除旧分类，添加新分类
            del subcategories[old_name]
            subcategories[new_name] = sub_data

            self.save_config()
            return True
        except Exception as e:
            print(f"重命名次级分类失败: {e}")
            return False

    def rename_child_category(self, parent_name: str, sub_name: str, old_name: str, new_name: str, new_desc: str = "") -> bool:
        """重命名子级分类"""
        try:
            sub_cat = (self.config.get("categories", {})
                      .get(parent_name, {})
                      .get("subcategories", {})
                      .get(sub_name, {}))

            child_subcategories = sub_cat.get("subcategories", {})
            if old_name not in child_subcategories or new_name in child_subcategories:
                return False

            # 复制分类数据
            child_data = child_subcategories[old_name].copy()
            if new_desc:
                child_data["description"] = new_desc

            # 更新所有配置组的分类路径
            old_path = f"{parent_name}/{sub_name}/{old_name}"
            new_path = f"{parent_name}/{sub_name}/{new_name}"
            configs = child_data.get("configs", [])
            for config_name in configs:
                if config_name in self.config["groups"]:
                    self.config["groups"][config_name]["category_path"] = new_path

            # 删除旧分类，添加新分类
            del child_subcategories[old_name]
            child_subcategories[new_name] = child_data

            self.save_config()
            return True
        except Exception as e:
            print(f"重命名子级分类失败: {e}")
            return False

    def _update_config_paths_for_parent_rename(self, old_parent: str, new_parent: str):
        """更新父级分类重命名后的配置组路径"""
        try:
            for config_name, config_data in self.config["groups"].items():
                category_path = config_data.get("category_path", "")
                if category_path.startswith(f"{old_parent}/"):
                    new_path = category_path.replace(f"{old_parent}/", f"{new_parent}/", 1)
                    config_data["category_path"] = new_path
        except Exception as e:
            print(f"更新配置组路径失败: {e}")

    def _update_config_paths_for_sub_rename(self, parent_name: str, old_sub: str, new_sub: str):
        """更新次级分类重命名后的配置组路径"""
        try:
            old_prefix = f"{parent_name}/{old_sub}/"
            new_prefix = f"{parent_name}/{new_sub}/"

            for config_name, config_data in self.config["groups"].items():
                category_path = config_data.get("category_path", "")
                if category_path.startswith(old_prefix):
                    new_path = category_path.replace(old_prefix, new_prefix, 1)
                    config_data["category_path"] = new_path
        except Exception as e:
            print(f"更新配置组路径失败: {e}")

    def _remove_config_from_old_path(self, config_name: str, old_category_path: str):
        """从旧分类路径中移除配置组"""
        try:
            old_parts = old_category_path.split("/")
            if len(old_parts) == 3:
                # 3级结构
                old_parent, old_sub, old_child = old_parts
                old_target_cat = (self.config.get("categories", {})
                                .get(old_parent, {})
                                .get("subcategories", {})
                                .get(old_sub, {})
                                .get("subcategories", {})
                                .get(old_child, {}))
            elif len(old_parts) == 4:
                # 4级结构
                old_parent, old_sub, old_third, old_fourth = old_parts
                old_target_cat = (self.config.get("categories", {})
                                .get(old_parent, {})
                                .get("subcategories", {})
                                .get(old_sub, {})
                                .get("subcategories", {})
                                .get(old_third, {})
                                .get("subcategories", {})
                                .get(old_fourth, {}))
            else:
                return

            if "configs" in old_target_cat and config_name in old_target_cat["configs"]:
                old_target_cat["configs"].remove(config_name)
        except Exception as e:
            print(f"从旧路径移除配置组失败: {e}")

    def add_four_level_category(self, parent: str, sub: str, third: str, fourth: str, description: str = "") -> bool:
        """添加四级分类"""
        try:
            categories = self.config.setdefault("categories", OrderedDict())

            # 确保父级分类存在
            if parent not in categories:
                categories[parent] = {
                    "description": f"{parent}分类",
                    "subcategories": OrderedDict(),
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }

            # 确保次级分类存在
            parent_cat = categories[parent]
            subcategories = parent_cat.setdefault("subcategories", OrderedDict())
            if sub not in subcategories:
                subcategories[sub] = {
                    "description": f"{sub}分类",
                    "subcategories": OrderedDict()
                }

            # 确保第三级分类存在
            sub_cat = subcategories[sub]
            third_subcategories = sub_cat.setdefault("subcategories", OrderedDict())
            if third not in third_subcategories:
                third_subcategories[third] = {
                    "description": f"{third}配置",
                    "subcategories": OrderedDict()
                }

            # 添加第四级分类
            third_cat = third_subcategories[third]
            fourth_subcategories = third_cat.setdefault("subcategories", OrderedDict())
            if fourth not in fourth_subcategories:
                fourth_subcategories[fourth] = {
                    "configs": [],
                    "description": description or f"{fourth}配置"
                }

                # 更新时间戳
                parent_cat["updated_at"] = datetime.now().isoformat()
                self.save_config()
                return True

            return False
        except Exception as e:
            print(f"添加四级分类失败: {e}")
            return False

    # ==================== 新增：缓存管理功能 ====================

    def get_config_cache(self, config_name: str) -> Optional[Dict[str, Any]]:
        """获取配置组的缓存信息"""
        config = self.config["groups"].get(config_name)
        if config:
            return config.get("cache", {})
        return None

    def update_config_cache(self, config_name: str, cache_data: Dict[str, Any]) -> bool:
        """更新配置组的缓存信息"""
        try:
            if config_name not in self.config["groups"]:
                return False

            if "cache" not in self.config["groups"][config_name]:
                self.config["groups"][config_name]["cache"] = {}

            # 更新缓存数据
            self.config["groups"][config_name]["cache"].update(cache_data)
            self.config["groups"][config_name]["cache"]["updated_at"] = datetime.now().isoformat()

            self.save_config()
            return True
        except Exception as e:
            print(f"更新缓存失败: {e}")
            return False

    def add_url_to_cache(self, config_name: str, url: str, title: str, timestamp: str = None) -> bool:
        """添加URL到缓存记录（保持最新3条）"""
        try:
            if config_name not in self.config["groups"]:
                return False

            if "cache" not in self.config["groups"][config_name]:
                self.config["groups"][config_name]["cache"] = {
                    "last_update": None,
                    "last_urls": [],
                    "total_articles": 0,
                    "success_rate": 0.0,
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }

            cache = self.config["groups"][config_name]["cache"]

            # 创建新的URL记录
            url_record = {
                "url": url,
                "title": title,
                "timestamp": timestamp or datetime.now().isoformat()
            }

            # 检查是否已存在相同URL，如果存在则更新
            existing_index = -1
            for i, existing_url in enumerate(cache.get("last_urls", [])):
                if existing_url["url"] == url:
                    existing_index = i
                    break

            if existing_index >= 0:
                # 更新现有记录
                cache["last_urls"][existing_index] = url_record
            else:
                # 添加新记录
                cache.setdefault("last_urls", []).insert(0, url_record)

            # 保持最新3条记录
            cache["last_urls"] = cache["last_urls"][:3]

            # 更新最后更新时间
            cache["last_update"] = datetime.now().isoformat()
            cache["updated_at"] = datetime.now().isoformat()

            self.save_config()
            return True
        except Exception as e:
            print(f"添加URL到缓存失败: {e}")
            return False

    def get_last_urls(self, config_name: str) -> List[Dict[str, str]]:
        """获取配置组的最后3条URL记录"""
        cache = self.get_config_cache(config_name)
        if cache:
            return cache.get("last_urls", [])
        return []

    def clear_config_cache(self, config_name: str) -> bool:
        """清空配置组的缓存"""
        try:
            if config_name not in self.config["groups"]:
                return False

            self.config["groups"][config_name]["cache"] = {
                "last_update": None,
                "last_urls": [],
                "total_articles": 0,
                "success_rate": 0.0,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }

            self.save_config()
            return True
        except Exception as e:
            print(f"清空缓存失败: {e}")
            return False