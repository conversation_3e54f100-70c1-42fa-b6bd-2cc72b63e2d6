{"last_used": "上海政协_提案工作", "categories": {"政府机构": {"description": "政府机构相关配置", "subcategories": {"人大系统": {"description": "人大系统配置", "subcategories": {"全国人大": {"subcategories": {"常委会工作": {"configs": [], "description": "全国人大常委会工作"}, "代表工作": {"configs": [], "description": "全国人大代表工作"}}, "description": "全国人大配置"}, "北京人大": {"subcategories": {"监督纵横": {"configs": ["北京人大"], "description": "北京人大监督工作"}, "代表工作": {"configs": [], "description": "北京人大代表工作"}}, "description": "北京人大配置"}, "宁波人大": {"subcategories": {"监督纵横": {"configs": ["宁波人大"], "description": "宁波人大监督工作"}, "代表工作": {"configs": [], "description": "宁波人大代表工作"}}, "description": "宁波人大配置"}, "杭州人大": {"subcategories": {"监督纵横": {"configs": ["杭州人大"], "description": "杭州人大监督工作"}, "代表工作": {"configs": [], "description": "杭州人大代表工作"}}, "description": "杭州人大配置"}, "上海人大": {"subcategories": {"监督纵横": {"configs": ["上海人大"], "description": "上海人大监督工作"}, "代表工作": {"configs": [], "description": "上海人大代表工作"}}, "description": "上海人大配置"}}}, "政协系统": {"description": "政协系统配置", "subcategories": {"全国政协": {"subcategories": {"提案工作": {"configs": [], "description": "全国政协提案工作"}, "委员工作": {"configs": [], "description": "全国政协委员工作"}}, "description": "全国政协配置"}, "珠海政协": {"subcategories": {"提案工作": {"configs": ["珠海政协"], "description": "珠海政协提案工作"}, "委员工作": {"configs": [], "description": "珠海政协委员工作"}}, "description": "珠海政协配置"}, "澳门政协": {"subcategories": {"提案工作": {"configs": ["澳门政协"], "description": "澳门政协提案工作"}, "委员工作": {"configs": [], "description": "澳门政协委员工作"}}, "description": "澳门政协配置"}, "重庆政协": {"subcategories": {"提案工作": {"configs": ["重庆政协"], "description": "重庆政协提案工作"}, "委员工作": {"configs": [], "description": "重庆政协委员工作"}}, "description": "重庆政协配置"}, "上海政协": {"subcategories": {"提案工作": {"configs": ["上海政协_提案工作"], "description": "上海政协提案工作"}, "委员工作": {"configs": [], "description": "上海政协委员工作"}}, "description": "上海政协配置"}, "天津政协": {"subcategories": {"提案工作": {"configs": ["天津政协_提案工作"], "description": "天津政协提案工作"}, "委员工作": {"configs": [], "description": "天津政协委员工作"}}, "description": "天津政协配置"}, "北京政协": {"subcategories": {"提案工作": {"configs": ["北京政协_提案工作"], "description": "北京政协提案工作"}, "委员工作": {"configs": [], "description": "北京政协委员工作"}}, "description": "北京政协配置"}, "银川政协": {"subcategories": {"提案工作": {"configs": ["银川政协_提案工作"], "description": "银川政协提案工作"}, "委员工作": {"configs": [], "description": "银川政协委员工作"}}, "description": "银川政协配置"}, "南宁政协": {"subcategories": {"提案工作": {"configs": ["南宁政协_提案工作"], "description": "南宁政协提案工作"}, "委员工作": {"configs": [], "description": "南宁政协委员工作"}}, "description": "南宁政协配置"}, "成都政协": {"subcategories": {"提案工作": {"configs": ["成都政协_提案工作"], "description": "成都政协提案工作"}, "委员工作": {"configs": [], "description": "成都政协委员工作"}}, "description": "成都政协配置"}, "海南政协": {"subcategories": {"提案工作": {"configs": ["海南政协_提案工作"], "description": "海南政协提案工作"}, "委员工作": {"configs": ["海南政协"], "description": "海南政协委员工作"}}, "description": "海南政协配置"}}}}, "created_at": "2025-07-23T11:31:00.958560", "updated_at": "2025-07-23T11:31:00.958560"}, "新闻媒体": {"description": "新闻媒体相关配置", "subcategories": {"中央媒体": {"description": "中央媒体配置", "subcategories": {"人民日报": {"subcategories": {"时政新闻": {"configs": [], "description": "人民日报时政新闻"}, "评论文章": {"configs": [], "description": "人民日报评论文章"}}, "description": "人民日报相关配置"}, "新华社": {"subcategories": {"时政新闻": {"configs": [], "description": "新华社时政新闻"}, "国际新闻": {"configs": [], "description": "新华社国际新闻"}}, "description": "新华社相关配置"}}}}, "created_at": "2025-07-23T11:31:00.958560", "updated_at": "2025-07-23T11:31:00.958560"}}, "groups": {"北京人大": {"input_url": "https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/", "base_url": "https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/", "max_pages": "1", "url_mode": "relative", "page_suffix": "index_{n}.html", "list_container_selector": ".ty_gl.container", "article_item_selector": ".ty_list li", "title_selectors": [".xl_title.clearfix h1"], "content_selector": ".z<PERSON><PERSON><PERSON>", "content_type": "CSS", "date_selectors": [".xl_ly span:first-child"], "source_selectors": [".xl_ly span:last-child"], "mode": "balance", "collect_links": true, "headless": true, "export_filename": "", "file_format": "CSV", "classid": "", "max_workers": 5, "retry": 2, "interval": 0.0, "pagination_config": {"enabled": false, "pagination_type": "禁用动态翻页", "next_button_selector": "a.next:not(.lose)", "wait_after_click": 2000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": "body", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ""}, "category": "中国人大", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/人大系统/北京人大/监督纵横"}, "宁波人大": {"input_url": "https://www.nbrd.gov.cn/col/col1229576425/index.html", "base_url": "https://www.nbrd.gov.cn/col/col1229576425/", "max_pages": "2", "list_container_selector": ".default_pgContainer ul", "article_item_selector": ".default_pgContainer ul li", "title_selectors": [".z_title color02"], "content_selectors": [".article_cont"], "content_type": "CSS", "date_selectors": ["//li[contains(@class", "\"color02\") and contains(text()", "\"时间："], "source_selectors": ["//li[contains(@class", "\"color02\") and contains(text()", "\"来源："], "page_suffix": "index.html?uid=8022500&pageNum={n}", "page_suffix_start": 2, "url_mode": "绝对", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": true, "mode": "balance", "filters": [], "category": "中国人大", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/人大系统/宁波人大/监督纵横", "start_page": "1", "export_filename": "", "file_format": "CSV", "excel_write_strategy": "auto", "classid": "", "max_workers": 5, "retry": 2, "interval": 0.0, "skip_pagination_if_cached": false, "pagination_config": {"enabled": false, "pagination_type": "禁用动态翻页", "next_button_selector": "a.next:not(.lose)", "content_ready_selector": "", "wait_after_click": 2000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": "body", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": "", "stop_on_duplicate_last_item": true, "excel_file_path": "manual_pagination/url_templates.xlsx", "manual_wait_between_pages": 2000, "save_progress": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}}, "杭州人大": {"input_url": "https://www.hzrd.gov.cn/col/col1229690487/index.html", "base_url": "https://www.hzrd.gov.cn/col/col1229690487/", "max_pages": "3", "url_mode": "absolute", "page_suffix": "index.html?uid=7856661&pageNum={n}", "list_container_selector": ".lists_sub_1 ul", "article_item_selector": "li", "title_selectors": [".article_title"], "content_selector": ".article_article", "content_type": "CSS", "date_selectors": [".article_time span:first-child"], "source_selectors": [".article_time span:nth-child(2)"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "C:/Users/<USER>/Desktop/新建文件夹/crawler 2/articles/杭州人大_代表风采", "file_format": "Excel", "classid": "3805", "max_workers": 8, "retry": 2, "interval": 0.6, "filters": [], "pagination_config": {"enabled": true, "pagination_type": "点击翻页", "next_button_selector": "a.default_pgBtn.default_pgNext", "content_ready_selector": "a.default_pgBtn.default_pgNext.default_pgNextDisabled", "wait_after_click": 2000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": "body", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": "", "stop_on_duplicate_last_item": true}, "category": "中国人大", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/人大系统/杭州人大/监督纵横"}, "上海人大": {"input_url": "https://www.shrd.gov.cn/n8347/n8379/n8381/index.html", "base_url": "https://www.shrd.gov.cn/n8347/n8379/n8381/", "max_pages": "1", "url_mode": "absolute", "page_suffix": "index.html?uid=7856661&pageNum={n}", "list_container_selector": ".gdlist", "article_item_selector": "li", "title_selectors": ["h1.blue30"], "content_selector": ".gjzw.gray16", "content_type": "CSS", "date_selectors": [".gray14"], "source_selectors": [".gray14"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/基层人大（2）.xlsx", "file_format": "Excel", "classid": "3806", "max_workers": 8, "retry": 2, "interval": 0.3, "filters": [], "pagination_config": {"enabled": true, "pagination_type": "滚动翻页", "next_button_selector": "a.default_pgBtn.default_pgNext", "content_ready_selector": "a.default_pgBtn.default_pgNext.default_pgNextDisabled", "wait_after_click": 2000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "list_container_type": "CSS", "article_item_type": "CSS", "config_group": "", "skip_pagination_if_cached": true, "excel_write_strategy": "smart", "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国人大", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/人大系统/上海人大/监督纵横"}, "珠海政协": {"input_url": "http://www.zhzx.gov.cn/zwhgz/rkzyhjw/", "base_url": "http://www.zhzx.gov.cn/zwhgz/rkzyhjw/", "max_pages": "999999", "url_mode": "absolute", "page_suffix": "index_{n}.html", "list_container_selector": "div.column_800", "article_item_selector": "div.list ul li", "title_selectors": [".article_tit"], "content_selectors": [".TRS_Editor", "div.view.TRS_UEDITOR.trs_paper_default.trs_web", ".article_cont", "div[class*='content']", "div.article_con", "div.z<PERSON><PERSON><PERSON>"], "content_type": "CSS", "date_selectors": ["div.article_date"], "source_selectors": ["div.article_date"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/珠海政协_人口资源环境委", "file_format": "Excel", "classid": "3805", "max_workers": 8, "retry": 2, "interval": 0.6, "filters": [], "pagination_config": {"enabled": false, "pagination_type": "滚动翻页", "next_button_selector": "a.default_pgBtn.default_pgNext", "content_ready_selector": "a.default_pgBtn.default_pgNext.default_pgNextDisabled", "wait_after_click": 2000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "config_group": "", "skip_pagination_if_cached": true, "excel_write_strategy": "smart", "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/政协系统/珠海政协/提案工作"}, "澳门政协": {"input_url": "https://www.gov.mo/zh-hant/about-government/policy-consultation/policy-consultation-list/?category_id&entity_id&consultation_year", "base_url": "https://www.gov.mo/zh-hant/about-government/policy-consultation/policy-consultation-list/", "max_pages": "999999", "url_mode": "absolute", "page_suffix": "page/{n}/?category_id&entity_id&consultation_year", "list_container_selector": ".filter-results-list", "article_item_selector": "li", "title_selectors": ["h1"], "content_selectors": [".mce-content-body"], "date_selectors": ["time.date"], "source_selectors": ["aside.metadata-list dd a"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/澳门政协_参政建言", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 8, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": true, "pagination_config": {"enabled": false, "pagination_type": "滚动翻页", "next_button_selector": "a.default_pgBtn.default_pgNext", "content_ready_selector": "a.default_pgBtn.default_pgNext.default_pgNextDisabled", "wait_after_click": 2000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/政协系统/澳门政协/提案工作"}, "重庆政协": {"input_url": "http://www.cqzx.gov.cn/cqzx_class/node_6740.htm", "base_url": "http://www.cqzx.gov.cn/cqzx_class/", "max_pages": "999999", "url_mode": "absolute", "page_suffix": "node_6736_{n}.htm", "list_container_selector": ".news_list_noimg ul", "article_item_selector": "li", "title_selectors": [".article_title"], "content_selectors": [".content2"], "date_selectors": [".time"], "source_selectors": [".time"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/重庆政协_参政建言", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 8, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": true, "pagination_config": {"enabled": false, "pagination_type": "滚动翻页", "next_button_selector": "a.default_pgBtn.default_pgNext", "content_ready_selector": "a.default_pgBtn.default_pgNext.default_pgNextDisabled", "wait_after_click": 2000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/政协系统/重庆政协/提案工作"}, "上海政协_提案工作": {"input_url": "http://www.cqzx.gov.cn/cqzx_class/node_6740.htm", "base_url": "http://www.cqzx.gov.cn/cqzx_class/", "max_pages": "999999", "url_mode": "绝对", "page_suffix": "node_6736_{n}.htm", "list_container_selector": ".news_list_noimg ul", "article_item_selector": "li", "title_selectors": [".article_title"], "content_selectors": [".content2"], "date_selectors": [".time"], "source_selectors": [".time"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/重庆政协_参政建言", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 8, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": true, "pagination_config": {"enabled": false, "pagination_type": "滚动翻页", "next_button_selector": "a.default_pgBtn.default_pgNext", "content_ready_selector": "a.default_pgBtn.default_pgNext.default_pgNextDisabled", "wait_after_click": 2000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true, "excel_file_path": "manual_pagination/url_templates.xlsx", "manual_wait_between_pages": 2000, "save_progress": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/政协系统/上海政协/提案工作", "start_page": "1"}, "天津政协_提案工作": {"input_url": "https://www.tjszx.gov.cn/tagz/taxd/index.shtml", "base_url": "https://www.tjszx.gov.cn/tagz/", "max_pages": "999", "url_mode": "absolute", "page_suffix": "system/count//0009003/000000000000/000/000/c0009003000000000000_00000001{n}.shtml", "list_container_selector": "//table[@width=\"1150\"]", "article_item_selector": "tr", "title_selectors": ["td.<PERSON><PERSON><PERSON>"], "content_selectors": ["td.<PERSON><PERSON><PERSON><PERSON>"], "date_selectors": ["td.xinxi"], "source_selectors": ["td.xinxi"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/天津政协_提案工作", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 8, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": false, "pagination_config": {"enabled": true, "pagination_type": "点击翻页", "next_button_selector": "//a[@class=\"next\" and contains(@onclick, \"nextpage\")]", "content_ready_selector": "", "wait_after_click": 3000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "start_page": "0", "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/政协系统/天津政协/提案工作"}, "北京政协_提案工作": {"input_url": "https://www.bjzx.gov.cn/zxgz/zxdt/", "base_url": "https://www.bjzx.gov.cn/zxgz/zxdt/", "max_pages": "999", "start_page": "0", "url_mode": "absolute", "page_suffix": "index_{n}.html", "list_container_selector": ".main glListM", "article_item_selector": "ul li", "title_selectors": ["div.title2024 h1 p"], "content_selectors": ["div.TRS_Editor"], "date_selectors": ["div#othermessage p.fl span:first-child"], "source_selectors": ["div#othermessage p.fl span:nth-child(2)"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/北京政协_提案工作", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 8, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": false, "pagination_config": {"enabled": true, "pagination_type": "手动翻页", "next_button_selector": "//a[@class=\"next\" and contains(@onclick, \"nextpage\")]", "content_ready_selector": "", "wait_after_click": 3000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true, "excel_file_path": "manual_pagination/url_templates.xlsx", "manual_wait_between_pages": 2000, "save_progress": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/政协系统/北京政协/提案工作"}, "银川政协_提案工作": {"input_url": "https://www.zxycswh.gov.cn/yzjy/index.html", "base_url": "https://www.zxycswh.gov.cn/yzjy/", "max_pages": "999", "start_page": "1", "url_mode": "absolute", "page_suffix": "index_{n}.html", "list_container_selector": "ul", "article_item_selector": "li", "title_selectors": ["h2"], "content_selectors": ["div.article"], "date_selectors": [".fb-date"], "source_selectors": ["//div[@style=\"text-align: center;\"]/text()[last()]"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/银川政协_提案工作", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 8, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": true, "pagination_config": {"enabled": false, "pagination_type": "手动翻页", "next_button_selector": "//a[@class=\"next\" and contains(@onclick, \"nextpage\")]", "content_ready_selector": "", "wait_after_click": 3000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true, "excel_file_path": "manual_pagination/url_templates.xlsx", "manual_wait_between_pages": 2000, "save_progress": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/政协系统/银川政协/提案工作"}, "南宁政协_提案工作": {"input_url": "https://www.gxnnszx.gov.cn/tagz/", "base_url": "https://www.gxnnszx.gov.cn/tagz/", "max_pages": "999", "start_page": "1", "url_mode": "absolute", "page_suffix": "index_{n}.html", "list_container_selector": "ul.list", "article_item_selector": "li", "title_selectors": ["h2"], "content_selectors": [".TRS_UEDITOR"], "date_selectors": [".aticle_xx span:first-child"], "source_selectors": [".aticle_xx span:nth-child(2)"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/南宁政协_提案工作", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 8, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": true, "pagination_config": {"enabled": false, "pagination_type": "手动翻页", "next_button_selector": "//a[@class=\"next\" and contains(@onclick, \"nextpage\")]", "content_ready_selector": "", "wait_after_click": 3000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true, "excel_file_path": "manual_pagination/url_templates.xlsx", "manual_wait_between_pages": 2000, "save_progress": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/政协系统/南宁政协/提案工作"}, "呼和浩特市_提案工作": {"input_url": "https://www.hhhtzx.gov.cn/tagz/index.jhtml", "base_url": "https://www.hhhtzx.gov.cn/tagz/index.jhtml", "max_pages": "999", "start_page": "1", "url_mode": "absolute", "page_suffix": "index_{n}.jhtml", "list_container_selector": "div.post", "article_item_selector": "h3.post-head > a", "title_selectors": ["h2"], "content_selectors": [".newsCon"], "date_selectors": [".post-time"], "source_selectors": [".newsCon > hr + p > span"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/呼和浩特市_提案工作", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 8, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": true, "pagination_config": {"enabled": false, "pagination_type": "手动翻页", "next_button_selector": "//a[@class=\"next\" and contains(@onclick, \"nextpage\")]", "content_ready_selector": "", "wait_after_click": 3000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true, "excel_file_path": "manual_pagination/url_templates.xlsx", "manual_wait_between_pages": 2000, "save_progress": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/人大系统/地方人大"}, "昆明市_提案工作": {"input_url": "http://www.kmszx.gov.cn/lzhd/xshd/", "base_url": "http://www.kmszx.gov.cn/lzhd/xshd/", "max_pages": "999", "start_page": "1", "url_mode": "absolute", "page_suffix": "index_{n}.shtml", "list_container_selector": "ul.list", "article_item_selector": "li", "title_selectors": [".detail-title h3"], "content_selectors": [".detail-content"], "date_selectors": ["div.detail-title p span:nth-child(2)"], "source_selectors": ["div.detail-title p"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/昆明政协_参言建议", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 8, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": true, "pagination_config": {"enabled": false, "pagination_type": "手动翻页", "next_button_selector": "//a[@class=\"next\" and contains(@onclick, \"nextpage\")]", "content_ready_selector": "", "wait_after_click": 3000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true, "excel_file_path": "manual_pagination/url_templates.xlsx", "manual_wait_between_pages": 2000, "save_progress": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/人大系统/地方人大"}, "成都政协_提案工作": {"input_url": "https://www.cdcppcc.gov.cn/html/weiyuanluzhi/weiyuan<PERSON>an/", "base_url": "https://www.cdcppcc.gov.cn/html/weiyuanluzhi/weiyuan<PERSON>an/", "max_pages": "999", "start_page": "1", "url_mode": "absolute", "page_suffix": "{n}.html", "list_container_selector": "ul.list-text", "article_item_selector": "li.cont", "title_selectors": [".show-title"], "content_selectors": [".show-content"], "date_selectors": [".show-inputtime"], "source_selectors": [".show-source"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/成都政协_提案工作", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 5, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": true, "pagination_config": {"enabled": false, "pagination_type": "手动翻页", "next_button_selector": "//a[@class=\"next\" and contains(@onclick, \"nextpage\")]", "content_ready_selector": "", "wait_after_click": 3000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true, "excel_file_path": "manual_pagination/url_templates.xlsx", "manual_wait_between_pages": 2000, "save_progress": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/政协系统/成都政协/提案工作"}, "海南政协_提案工作": {"input_url": "https://zx.haikou.gov.cn/sqmy/", "base_url": "https://zx.haikou.gov.cn/sqmy/", "max_pages": "999", "start_page": "0", "url_mode": "absolute", "page_suffix": "index_{n}.shtml", "list_container_selector": ".list-c", "article_item_selector": "li .cont", "title_selectors": [".maincon-t"], "content_selectors": [".maincon-c"], "date_selectors": [".maincon-i"], "source_selectors": [".maincon-i"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.1 - P/articles/海南政协_提案工作", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 5, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": true, "pagination_config": {"enabled": false, "pagination_type": "手动翻页", "next_button_selector": "//a[@class=\"next\" and contains(@onclick, \"nextpage\")]", "content_ready_selector": "", "wait_after_click": 3000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true, "excel_file_path": "manual_pagination/url_templates.xlsx", "manual_wait_between_pages": 2000, "save_progress": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category": "中国政协", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T14:58:30.874431", "updated_at": "2025-07-22T14:58:30.874431"}, "category_path": "政府机构/政协系统/海南政协/提案工作"}, "海南政协": {"input_url": "https://zx.haikou.gov.cn/sqmy/", "base_url": "https://zx.haikou.gov.cn/sqmy/", "max_pages": "999", "start_page": "0", "url_mode": "绝对", "page_suffix": "index_{n}.shtml", "list_container_selector": ".list-c", "article_item_selector": "li", "title_selectors": [".maincon-t"], "content_selectors": [".maincon-c"], "date_selectors": [".maincon-i"], "source_selectors": [".maincon-i"], "mode": "safe", "collect_links": false, "headless": true, "export_filename": "D:/信息/全国人大/crawler 0.4.2- P/articles/海南政协_提案工作.xlsx", "file_format": "Excel", "excel_write_strategy": "smart", "classid": "3805", "max_workers": 5, "retry": 2, "interval": 0.6, "filters": [], "config_group": "", "skip_pagination_if_cached": true, "pagination_config": {"enabled": false, "pagination_type": "手动翻页", "next_button_selector": "//a[@class=\"next\" and contains(@onclick, \"nextpage\")]", "content_ready_selector": "", "wait_after_click": 3000, "timeout": 10000, "disabled_check": true, "scroll_container_selector": ".largeData", "scroll_step": 800, "scroll_delay": 2000, "load_indicator_selector": ".load{n}", "stop_on_duplicate_last_item": true, "excel_file_path": "manual_pagination/url_templates.xlsx", "manual_wait_between_pages": 2000, "save_progress": true}, "module_config": {"enabled": true, "config_file": "configs/modules/module_configs.json"}, "field_config": {"use_field_config": false, "field_preset": "", "custom_field_list": []}, "category_path": "政府机构/政协系统/海南政协/委员工作", "cache": {"last_update": null, "last_urls": [], "total_articles": 0, "success_rate": 0.0, "created_at": "2025-07-22T16:32:21.224782", "updated_at": "2025-07-22T16:32:21.224782"}}}}